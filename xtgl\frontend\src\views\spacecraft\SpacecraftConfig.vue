<template>
  <div class="single-config-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>单机配置</h2>
    </div>

    <!-- 主内容区域：搜索条件、操作按钮、表格、分页连成一块 -->
    <div class="main-content">
      <!-- 搜索条件区域 -->
      <div class="search-section">
        <div class="search-row">
          <!-- 航天器型号 -->
          <div class="search-item">
            <label>航天器型号：</label>
            <el-input
              v-model="searchForm.model"
              placeholder="请输入航天器型号"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
              @clear="handleSearch"
            >
              <template #suffix>
                <el-icon class="search-icon" @click="handleSearch">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>

          <!-- 航天器名称 -->
          <div class="search-item">
            <label>航天器名称：</label>
            <el-input
              v-model="searchForm.name"
              placeholder="请输入航天器名称"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
              @clear="handleSearch"
            >
              <template #suffix>
                <el-icon class="search-icon" @click="handleSearch">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>

          <!-- 负责人名称 -->
          <div class="search-item">
            <label>负责人名称：</label>
            <el-input
              v-model="searchForm.header"
              placeholder="请输入负责人名称"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
              @clear="handleSearch"
            >
              <template #suffix>
                <el-icon class="search-icon" @click="handleSearch">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>

          <!-- 所属单位 -->
          <div class="search-item">
            <label>所属单位：</label>
            <el-input
              v-model="searchForm.company"
              placeholder="请输入所属单位"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
              @clear="handleSearch"
            >
              <template #suffix>
                <el-icon class="search-icon" @click="handleSearch">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>

          <!-- 操作按钮区域 -->
          <div class="button-item">
            <div class="button-group">
              <el-button type="primary" @click="handleSearch" :loading="loading">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          v-loading="loading"
          element-loading-text="正在加载数据..."
        >
          <el-table-column prop="model" label="型号" width="120" />
          <el-table-column prop="name" label="名称" width="180" />
          <el-table-column prop="header" label="负责人" width="120" />
          <el-table-column prop="createTime" label="创建时间" width="120" />
          <el-table-column prop="company" label="所属单位" width="200" />
          <el-table-column prop="receiveTime" label="开始接收时间" width="160" />
          <el-table-column prop="status" label="卫星状态" width="120" />
          <el-table-column label="遥测代号" width="120">
            <template #default="scope">
              <el-button
                type="text"
                @click="handleViewTelemetry(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button
                type="text"
                style="color: #409EFF"
                @click="handleConfig(scope.row)"
              >
                配置
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { craftSearch } from '@/api/single'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  model: '',
  name: '',
  header: '',
  company: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})



// 搜索航天器数据
const searchData = async () => {
  loading.value = true
  try {
    // 过滤空值参数
    const params = {}
    Object.keys(searchForm).forEach(key => {
      if (searchForm[key] && searchForm[key].trim()) {
        params[key] = searchForm[key].trim()
      }
    })

    // 添加分页参数
    params.pageNo = pagination.page
    params.pageSize = pagination.size

    console.log('搜索参数:', params)

    const response = await craftSearch(params)

    if (response.code === 200) {
      tableData.value = response.data.list || []
      pagination.total = response.data.total || 0
      pagination.page = response.data.page || 1
      pagination.size = response.data.size || 10

      // 搜索成功提示
      if (Object.keys(params).length > 2) { // 除了分页参数外还有其他搜索条件
        ElMessage.success(`搜索完成，共找到 ${pagination.total} 条记录`)
      }
    } else {
      ElMessage.error(response.message || '查询失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请检查网络连接或稍后重试')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索按钮点击
const handleSearch = () => {
  pagination.page = 1
  searchData()
}

// 重置按钮点击
const handleReset = () => {
  // 清空搜索表单
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })

  // 重置分页
  pagination.page = 1

  // 重新搜索
  searchData()

  ElMessage.info('搜索条件已重置')
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  searchData()
}

// 当前页改变
const handleCurrentChange = (val) => {
  pagination.page = val
  searchData()
}

// 查看遥测代号
const handleViewTelemetry = (row) => {
  ElMessage.info('遥测代号查看功能暂未开发')
  // TODO: 跳转到遥测代号查看页面
}

// 配置按钮点击
const handleConfig = (row) => {
  if (!row || !row.id || !row.name) {
    ElMessage.error('航天器数据不完整')
    return
  }

  router.push({
    path: '/spacecraft/config-main',
    query: {
      satelliteId: row.id,
      satelliteName: row.name
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  searchData()
})
</script>

<style scoped>
.single-config-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

/* 主内容区域：搜索、表格、分页连成一块 */
.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.search-section {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.search-row {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 200px;
}

.button-item {
  margin-left: auto;
  flex-shrink: 0;
}

.button-group {
  display: flex;
  gap: 10px;
}

.search-item label {
  white-space: nowrap;
  margin-right: 8px;
  color: #333;
  font-weight: 500;
  min-width: 80px;
}

.search-input {
  width: 160px;
}

.search-icon {
  color: #999;
  cursor: pointer;
}

.search-icon:hover {
  color: #409EFF;
}

.table-section {
  padding: 0 20px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .search-row {
    flex-wrap: wrap;
  }

  .search-item {
    min-width: 45%;
    margin-bottom: 10px;
  }

  .button-item {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .search-item {
    min-width: 100%;
  }

  .search-input {
    width: 200px;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

/* 按钮样式优化 */
.button-group .el-button {
  min-width: 80px;
}

/* 加载状态优化 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}
</style>
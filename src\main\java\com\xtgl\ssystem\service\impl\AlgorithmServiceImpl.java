package com.xtgl.ssystem.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xtgl.ssystem.common.dto.AlgorithmDto;
import com.xtgl.ssystem.common.dto.AlgorithmSearchDto;
import com.xtgl.ssystem.common.entity.Algorithm;
import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.common.enums.AlgorithmType;
import com.xtgl.ssystem.mapper.AlgorithmMapper;
import com.xtgl.ssystem.service.AlgorithmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 算法服务实现类
 */
@Slf4j
@Service
public class AlgorithmServiceImpl implements AlgorithmService {

    @Autowired
    private AlgorithmMapper algorithmMapper;

    @Override
    public PageResult<AlgorithmDto> searchAlgorithms(AlgorithmSearchDto searchDto) {
        log.info("搜索算法: {}", searchDto);

        // 构建分页对象
        Page<Algorithm> page = new Page<>(searchDto.getPageNum(), searchDto.getPageSize());

        // 处理算法类型参数
        Integer typeCode = null;
        if (StringUtils.hasText(searchDto.getType())) {
            try {
                AlgorithmType algorithmType = AlgorithmType.fromName(searchDto.getType());
                typeCode = algorithmType.getCode();
            } catch (IllegalArgumentException e) {
                log.warn("无效的算法类型: {}", searchDto.getType());
            }
        }

        // 执行分页查询
        IPage<Algorithm> pageResult = algorithmMapper.searchAlgorithms(page, searchDto.getName(), typeCode);

        // 转换为DTO
        List<AlgorithmDto> algorithmDtos = pageResult.getRecords().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());

        // 构建分页结果
        PageResult<AlgorithmDto> result = new PageResult<>();
        result.setRecords(algorithmDtos);
        result.setTotal(pageResult.getTotal());
        result.setSize(pageResult.getSize());
        result.setCurrent(pageResult.getCurrent());
        result.setPages(pageResult.getPages());

        return result;
    }

    @Override
    public AlgorithmDto getAlgorithmById(Long id) {
        log.info("根据ID获取算法: {}", id);
        Algorithm algorithm = algorithmMapper.selectById(id);
        return algorithm != null ? convertToDto(algorithm) : null;
    }

    @Override
    public boolean updateAlgorithmStatus(Long id, Boolean enabled) {
        log.info("更新算法启用状态: id={}, enabled={}", id, enabled);

        // 先检查算法是否存在
        Algorithm existingAlgorithm = algorithmMapper.selectById(id);
        if (existingAlgorithm == null) {
            log.warn("算法不存在: id={}", id);
            return false;
        }

        Algorithm algorithm = new Algorithm();
        algorithm.setId(id);
        algorithm.setEnabled(enabled);

        int result = algorithmMapper.updateById(algorithm);
        boolean success = result > 0;

        if (success) {
            log.info("算法状态更新成功: id={}, enabled={}", id, enabled);
        } else {
            log.warn("算法状态更新失败: id={}, enabled={}", id, enabled);
        }

        return success;
    }

    /**
     * 转换为DTO
     */
    private AlgorithmDto convertToDto(Algorithm algorithm) {
        AlgorithmDto dto = new AlgorithmDto();
        BeanUtils.copyProperties(algorithm, dto);

        // 设置算法类型名称
        try {
            AlgorithmType algorithmType = AlgorithmType.fromCode(algorithm.getType());
            dto.setTypeName(algorithmType.getDescription());
        } catch (IllegalArgumentException e) {
            dto.setTypeName("未知类型");
        }

        // 设置启用状态文本
        dto.setEnabledText(Boolean.TRUE.equals(algorithm.getEnabled()) ? "启用" : "停用");

        return dto;
    }
}

package com.xtgl.ssystem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xtgl.ssystem.common.entity.TelemetryCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 遥测代号Mapper接口
 */
@Mapper
public interface TelemetryCodeMapper extends BaseMapper<TelemetryCode> {

    /**
     * 批量插入遥测代号
     *
     * @param telemetryCodes 遥测代号列表
     * @return 插入数量
     */
    int batchInsert(List<TelemetryCode> telemetryCodes);

    /**
     * 根据航天器ID删除遥测代号
     *
     * @param satelliteId 航天器ID
     * @return 删除数量
     */
    int deleteBySatelliteId(@Param("satelliteId") Long satelliteId);

    /**
     * 根据航天器ID查询遥测代号
     *
     * @param satelliteId 航天器ID
     * @return 遥测代号列表
     */
    List<TelemetryCode> selectBySatelliteId(@Param("satelliteId") Long satelliteId);
    


    /**
     * 根据文件名查询遥测代号（用于批次标识查询）
     *
     * @param fileName 文件名（批次标识）
     * @return 遥测代号列表
     */
    List<TelemetryCode> selectByFileName(@Param("fileName") String fileName);

    /**
     * 根据文件名删除遥测代号（用于清理临时数据）
     *
     * @param fileName 文件名（批次标识）
     * @return 删除数量
     */
    int deleteByFileName(@Param("fileName") String fileName);

    /**
     * 更新文件名对应的航天器ID
     *
     * @param satelliteId 航天器ID
     * @param fileName    文件名（批次标识）
     * @return 更新数量
     */
    int updateSatelliteIdByFileName(@Param("satelliteId") Long satelliteId, @Param("fileName") String fileName);

    /**
     * 根据分系统ID查询绑定的遥测代号
     *
     * @param subsystemId 分系统ID
     * @return 遥测代号列表
     */
    List<TelemetryCode> selectBySubsystemId(@Param("subsystemId") Long subsystemId);

    /**
     * 根据单机ID查询绑定的遥测代号
     *
     * @param singleId 单机ID
     * @return 遥测代号列表
     */
    List<TelemetryCode> selectBySingleId(@Param("singleId") Long singleId);

    /**
     * 根据模块ID查询绑定的遥测代号
     *
     * @param moduleId 模块ID
     * @return 遥测代号列表
     */
    List<TelemetryCode> selectByModuleId(@Param("moduleId") Long moduleId);

    /**
     * 根据遥测代号名称查询
     *
     * @param name 遥测代号名称
     * @return 遥测代号
     */
    TelemetryCode selectByName(@Param("name") String name);

    /**
     * 删除指定层级的遥测代号绑定
     *
     * @param level 层级类型
     * @param id    层级ID
     * @return 删除数量
     */
    int deleteByLevelAndId(@Param("level") String level, @Param("id") Long id);

    /**
     * 更新遥测代号的绑定层级
     *
     * @param telemetryCodeId 遥测代号ID
     * @param level           层级类型
     * @param levelId         层级ID
     * @return 更新数量
     */
    int updateTelemetryCodeBinding(@Param("telemetryCodeId") Long telemetryCodeId,
                                   @Param("level") String level,
                                   @Param("levelId") Long levelId);
}
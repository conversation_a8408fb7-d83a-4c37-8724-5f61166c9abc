<template>
  <div class="algorithm-container">
    <!-- 页面标题 -->
    <h2 class="page-title">算法管理</h2>

    <!-- 算法类型按钮组 -->
    <div class="type-buttons">
      <el-button
        v-for="type in algorithmTypes"
        :key="type.value"
        :type="selectedType === type.value ? 'primary' : 'default'"
        @click="handleTypeChange(type.value)"
      >
        {{ type.label }}
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-row">
        <div class="search-item">
          <span class="search-label">算法名称</span>
          <el-input
            v-model="searchForm.name"
            placeholder="请输入算法名称"
            clearable
            class="search-input"
            @keyup.enter="handleSearch"
            @clear="handleSearch"
          >
            <template #suffix>
              <el-icon class="search-icon" @click="handleSearch">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>
        <div class="search-buttons">
          <el-button type="primary" @click="handleSearch" :loading="loading">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
        element-loading-text="正在加载数据..."
      >
        <template #empty>
          <el-empty description="暂无数据" />
        </template>
        <el-table-column prop="name" label="算法名称" width="200" />
        <el-table-column prop="direction" label="擅长方向" width="150" />
        <el-table-column prop="description" label="介绍" min-width="300" />
        <el-table-column prop="enabledText" label="启停状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
              {{ scope.row.enabledText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialog.visible"
      title="编辑算法"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editDialog.form"
        label-width="100px"
      >
        <el-form-item label="算法名称">
          <el-input v-model="editDialog.form.name" disabled />
        </el-form-item>
        <el-form-item label="擅长方向">
          <el-input v-model="editDialog.form.direction" disabled />
        </el-form-item>
        <el-form-item label="介绍">
          <el-input
            v-model="editDialog.form.description"
            type="textarea"
            :rows="3"
            disabled
          />
        </el-form-item>
        <el-form-item label="启用状态" required>
          <el-radio-group v-model="editDialog.form.enabled">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit" :loading="editDialog.loading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { searchAlgorithms, getAlgorithmById, updateAlgorithmStatus } from '@/api/algorithm'

// 算法类型选项
const algorithmTypes = [
  { value: 'UNSUPERVISED', label: '无监督算法' },
  { value: 'SUPERVISED', label: '监督算法' },
  { value: 'DEEP_LEARNING', label: '深度学习算法' }
]

// 当前选中的算法类型
const selectedType = ref('UNSUPERVISED')

// 搜索表单
const searchForm = reactive({
  name: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 编辑对话框
const editDialog = reactive({
  visible: false,
  loading: false,
  form: {
    id: null,
    name: '',
    direction: '',
    description: '',
    enabled: true
  }
})

// 页面加载时初始化
onMounted(() => {
  searchData()
})

// 搜索数据
const searchData = async () => {
  loading.value = true
  try {
    const params = {
      name: searchForm.name || undefined,
      type: selectedType.value,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }

    const response = await searchAlgorithms(params)
    if (response.code === 200) {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.message || '获取数据失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('搜索算法失败', error)
    ElMessage.error('获取数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 算法类型切换
const handleTypeChange = (type) => {
  selectedType.value = type
  pagination.pageNum = 1
  searchData()
}

// 搜索按钮点击
const handleSearch = () => {
  pagination.pageNum = 1
  searchData()
}

// 重置按钮点击
const handleReset = () => {
  searchForm.name = ''
  pagination.pageNum = 1
  searchData()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.pageNum = 1
  searchData()
}

// 当前页改变
const handleCurrentChange = (val) => {
  pagination.pageNum = val
  searchData()
}

// 编辑按钮点击
const handleEdit = async (row) => {
  // 重置表单
  resetEditForm()

  try {
    const response = await getAlgorithmById(row.id)
    if (response.code === 200) {
      editDialog.form = {
        id: response.data.id,
        name: response.data.name,
        direction: response.data.direction,
        description: response.data.description,
        enabled: response.data.enabled
      }
      editDialog.visible = true
    } else {
      ElMessage.error(response.message || '获取算法详情失败')
    }
  } catch (error) {
    console.error('获取算法详情失败', error)
    ElMessage.error('获取算法详情失败：' + (error.message || '网络错误'))
  }
}

// 保存编辑
const handleSaveEdit = async () => {
  // 验证必填字段
  if (editDialog.form.enabled === null || editDialog.form.enabled === undefined) {
    ElMessage.warning('请选择启用状态')
    return
  }

  editDialog.loading = true
  try {
    const response = await updateAlgorithmStatus(editDialog.form.id, editDialog.form.enabled)
    if (response.code === 200) {
      ElMessage.success('更新成功')
      editDialog.visible = false
      searchData() // 刷新列表
    } else {
      ElMessage.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新算法状态失败', error)
    ElMessage.error('更新失败：' + (error.message || '网络错误'))
  } finally {
    editDialog.loading = false
  }
}

// 重置编辑表单
const resetEditForm = () => {
  editDialog.form = {
    id: null,
    name: '',
    direction: '',
    description: '',
    enabled: true
  }
}
</script>

<style scoped>
.algorithm-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 500;
  color: #333;
}

.type-buttons {
  margin-bottom: 20px;
}

.type-buttons .el-button {
  margin-right: 0;
  border-radius: 0;
}

.type-buttons .el-button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.type-buttons .el-button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.type-buttons .el-button:not(:last-child) {
  border-right: none;
}

/* 确保未选中按钮为灰底黑字 */
.type-buttons .el-button--default {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #606266;
}

.type-buttons .el-button--default:hover {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
  color: #409eff;
}

/* 确保选中按钮为蓝底白字 */
.type-buttons .el-button--primary {
  background-color: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.search-input {
  width: 250px;
}

.search-icon {
  cursor: pointer;
  color: #409EFF;
}

.search-buttons {
  display: flex;
  gap: 10px;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  background-color: #f5f7fa;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
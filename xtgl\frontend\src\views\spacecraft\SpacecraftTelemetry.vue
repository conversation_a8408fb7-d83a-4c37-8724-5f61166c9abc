<template>
  <div class="telemetry-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/spacecraft/list' }">
          <el-icon><HomeFilled /></el-icon>航天器列表
        </el-breadcrumb-item>
        <el-breadcrumb-item>遥测代号查看</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <!-- 标题区域 -->
    <div class="title-container">
      <div class="section-title">
        <div class="divider"></div>
        <h3>航天器遥测代号</h3>
      </div>
      <div class="title-buttons">
        <el-button type="success" @click="handleExport">导出数据</el-button>
        <el-button type="primary" @click="handleReturn">返回</el-button>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-container">
      <!-- 左侧树形控件 -->
      <div class="tree-container">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="defaultProps"
          highlight-current
          @node-click="handleNodeClick"
        />
      </div>
      
      <!-- 右侧表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
          height="calc(100vh - 250px)"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="code" label="遥测代号" min-width="120" />
          <el-table-column prop="description" label="代号描述" min-width="200" />
          <el-table-column prop="remark" label="备注" min-width="150" />
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.pageNo"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { HomeFilled } from '@element-plus/icons-vue'
import { getSatelliteById, getTelemetryData } from '@/api/satellite'
import { exportTelemetryData } from '@/utils/export'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const satelliteId = route.params.id
const tableData = ref([])
const treeRef = ref(null)
const satelliteName = ref('航天器')
const allTelemetryData = ref([])

// 分页配置
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

// 树形控件配置
const defaultProps = {
  children: 'children',
  label: 'label',
}

// 模拟树形数据结构 (后续替换为真实API数据)
const treeData = ref([
  {
    id: 1,
    label: '航天器名称',
    children: [
      {
        id: 2,
        label: '分系统名称1',
        children: [
          {
            id: 3,
            label: '单机名称1',
            children: [
              {
                id: 4,
                label: '模块名称1'
              },
              {
                id: 5,
                label: '模块名称2'
              }
            ]
          }
        ]
      },
      {
        id: 6,
        label: '分系统名称2',
        children: [
          {
            id: 7,
            label: '单机名称2',
            children: []
          }
        ]
      }
    ]
  }
])

// 初始化数据
onMounted(async () => {
  await fetchSatelliteInfo()
  await fetchTelemetryData()
})

// 获取航天器信息
const fetchSatelliteInfo = async () => {
  loading.value = true
  try {
    const res = await getSatelliteById(satelliteId)
    satelliteName.value = res.data.name || '航天器'
    // 更新树形结构的根节点名称
    if (treeData.value.length > 0) {
      treeData.value[0].label = satelliteName.value
    }
  } catch (error) {
    console.error('获取航天器信息失败', error)
    ElMessage.error('获取航天器信息失败')
  } finally {
    loading.value = false
  }
}

// 获取遥测代号数据
const fetchTelemetryData = async (nodeId = null) => {
  loading.value = true
  try {
    const res = await getTelemetryData({ satelliteId })
    const telemetryData = res.data || []

    // 保存所有数据用于导出
    allTelemetryData.value = telemetryData.map((item, index) => ({
      serialNum: index + 1,
      id: item.id,
      code: item.name || item.code,
      description: item.description,
      unit: item.unit || '',
      remark: item.note || ''
    }))

    // 更新分页数据
    updatePageData()
  } catch (error) {
    console.error('获取遥测代号数据失败', error)
    ElMessage.error('获取数据失败')
    tableData.value = []
    allTelemetryData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 更新分页数据
const updatePageData = () => {
  const startIndex = (pagination.pageNo - 1) * pagination.pageSize
  const endIndex = startIndex + pagination.pageSize

  tableData.value = allTelemetryData.value.slice(startIndex, endIndex)
  pagination.total = allTelemetryData.value.length
}

// 处理树节点点击
const handleNodeClick = (data) => {
  console.log('点击了树节点:', data)
  fetchTelemetryData(data.id)
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.pageNo = 1 // 重置到第一页
  updatePageData()
}

// 分页页码变化
const handleCurrentChange = (val) => {
  pagination.pageNo = val
  updatePageData()
}

// 返回按钮
const handleReturn = () => {
  router.push('/spacecraft/list')
}

// 处理导出
const handleExport = async () => {
  try {
    if (allTelemetryData.value.length === 0) {
      ElMessage.warning('没有数据可以导出')
      return
    }

    ElMessage.info('正在准备导出，请稍候...')

    // 使用前端导出
    await exportTelemetryData(allTelemetryData.value, satelliteName.value)

    ElMessage.success('导出成功！')
  } catch (error) {
    console.error('导出失败', error)
    ElMessage.error('导出失败：' + error.message)
  }
}
</script>

<style scoped>
.telemetry-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.breadcrumb {
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-buttons {
  display: flex;
  gap: 10px;
}

.section-title {
  display: flex;
  align-items: center;
}

.section-title .divider {
  width: 4px;
  height: 16px;
  background-color: #409EFF;
  margin-right: 8px;
  border-radius: 2px;
}

.section-title h3 {
  font-size: 16px;
  margin: 0;
  font-weight: bold;
}

.content-container {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
}

.tree-container {
  width: 300px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: auto;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 10px 0;
  border-radius: 4px;
}
</style> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xtgl.ssystem.mapper.TelemetryCodeMapper">

    <!-- 批量插入遥测代号 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO telemetry_code (
            serial_num, name, description, note, subsystem_id, 
            spacecraft_id, single_id, module_id, file_name
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.serialNum}, #{item.name}, #{item.description}, #{item.note},
                #{item.subsystemId}, #{item.spacecraftId}, #{item.singleId},
                #{item.moduleId}, #{item.fileName}
            )
        </foreach>
    </insert>

    <!-- 根据航天器ID查询遥测代号列表 -->
    <select id="selectBySatelliteId" resultType="com.xtgl.ssystem.common.entity.TelemetryCode">
        SELECT * FROM telemetry_code 
        WHERE spacecraft_id = #{satelliteId}
        ORDER BY serial_num
    </select>

    <!-- 根据航天器ID删除所有遥测代号 -->
    <delete id="deleteBySatelliteId">
        DELETE FROM telemetry_code WHERE spacecraft_id = #{satelliteId}
    </delete>



    <!-- 根据文件名查询遥测代号（用于批次标识查询） -->
    <select id="selectByFileName" resultType="com.xtgl.ssystem.common.entity.TelemetryCode">
        SELECT *
        FROM telemetry_code
        WHERE file_name = #{fileName}
        AND spacecraft_id IS NULL
        ORDER BY serial_num
    </select>

    <!-- 根据文件名删除遥测代号（用于清理临时数据） -->
    <delete id="deleteByFileName">
        DELETE FROM telemetry_code
        WHERE file_name = #{fileName}
        AND spacecraft_id IS NULL
    </delete>

    <!-- 更新文件名对应的航天器ID -->
    <update id="updateSatelliteIdByFileName">
        UPDATE telemetry_code
        SET spacecraft_id = #{satelliteId}
        WHERE file_name = #{fileName}
        AND spacecraft_id IS NULL
    </update>

    <!-- 根据分系统ID查询绑定的遥测代号（包括该分系统及其下级的所有绑定） -->
    <select id="selectBySubsystemId" resultType="com.xtgl.ssystem.common.entity.TelemetryCode">
        SELECT * FROM telemetry_code
        WHERE subsystem_id = #{subsystemId}
           OR single_id IN (SELECT id FROM single WHERE subsystem_id = #{subsystemId})
           OR module_id IN (SELECT id FROM module WHERE single_id IN (SELECT id FROM single WHERE subsystem_id = #{subsystemId}))
        ORDER BY serial_num
    </select>

    <!-- 根据单机ID查询绑定的遥测代号（包括该单机及其下级模块的所有绑定） -->
    <select id="selectBySingleId" resultType="com.xtgl.ssystem.common.entity.TelemetryCode">
        SELECT * FROM telemetry_code
        WHERE single_id = #{singleId}
           OR module_id IN (SELECT id FROM module WHERE single_id = #{singleId})
        ORDER BY serial_num
    </select>

    <!-- 根据模块ID查询绑定的遥测代号 -->
    <select id="selectByModuleId" resultType="com.xtgl.ssystem.common.entity.TelemetryCode">
        SELECT * FROM telemetry_code
        WHERE module_id = #{moduleId}
        ORDER BY serial_num
    </select>

    <!-- 根据遥测代号名称查询 -->
    <select id="selectByName" resultType="com.xtgl.ssystem.common.entity.TelemetryCode">
        SELECT * FROM telemetry_code
        WHERE name = #{name}
        LIMIT 1
    </select>

    <!-- 删除指定层级的遥测代号绑定 -->
    <delete id="deleteByLevelAndId">
        DELETE FROM telemetry_code
        WHERE
        <choose>
            <when test="level == 'satellite'">spacecraft_id = #{id}</when>
            <when test="level == 'subsystem'">subsystem_id = #{id}</when>
            <when test="level == 'single'">single_id = #{id}</when>
            <when test="level == 'module'">module_id = #{id}</when>
        </choose>
    </delete>

    <!-- 更新遥测代号的绑定层级 -->
    <update id="updateTelemetryCodeBinding">
        UPDATE telemetry_code
        SET
        <choose>
            <when test="level == 'subsystem'">subsystem_id = #{levelId}</when>
            <when test="level == 'single'">single_id = #{levelId}</when>
            <when test="level == 'module'">module_id = #{levelId}</when>
        </choose>
        WHERE id = #{telemetryCodeId}
    </update>

</mapper>